# File extensions
VALID_EXTENSIONS = {".csv", ".xls", ".xlsx", ".txt"}

# Distributor names and their variations
DISTRIBUTOR_MAPPING = {
    "natura": "American Distributing",
    "anp": "Anipet Animal Supplies Inc.",
    "sar": "ASLLC",
    "cpt": "Central Pet",
    "greenies fauna": "Fauna Foods Corp",
    "ftg": "Focus Technology Group/AG Data",
    "nutro": "General Pet Supply",
    "vdb": "John A. Van Den Bosch Co.",
    "psl": "Pet Science Ltd.",
    "pfp": "Phillips Feed & Pet Supply",
    "zig": "Zeiglers Distributor",
}
# Distributor names and their variations
DISTRIBUTOR_NAMES = {
    "ANP": ["ANP"],
    "PSL": ["PSL", "PSl"],
    "PFP": ["PFP"],
    "VDB": ["VDB"],
    "ZIG": ["ZIG"],
    "Natura": ["Natura"],
    "SAR": ["SAR", "Sar"],
    "CPT": ["CPT"],
    "FTG": ["FTG"],
    "Greenies": ["Greenies"],
    "Nutro": ["Nutro"],
}
# File types and their indicators
FILE_TYPE_MAPPINGS = {
    "sales": ["sales", "sale", "sls", "2"],
    "inventory": ["inventory", "inv", "stock", "3"],
    "store_mapping": ["store", "str", "location", "1"],
}

# Filename patterns configuration
FILENAME_PATTERNS = {
    "patterns": [
        {
            "name": "standard_with_filetype",
            "pattern": "^(\\d{4}-\\d{2}-\\d{2})_(\\d{3,4}(AM|PM))_([A-Za-z0-9]+)(?:_([A-Za-z]+))?(?:\\s+([A-Za-z]+))?(?:\\s+([A-Za-z]+\\s*[A-Za-z]*))?(?:\\s+([\\d\\-\\.]+))?(\\.\\w+)$",
            "components": [
                "date",
                "time",
                "distributor",
                "file_type",
                "file_type2",
                "product",
                "date_info",
                "extension",
            ],
        },
        {
            "name": "simple_without_filetype",
            "pattern": "^(\\d{4}-\\d{2}-\\d{2})_(\\d{3,4}(AM|PM))_([A-Za-z0-9]+)(\\.\\w+)$",
            "components": ["date", "time", "distributor", "extension"],
            "default_file_type": "sales",
        },
    ],
    "date_formats": ["YYYY-MM-DD"],
    "time_formats": ["HHMMAM", "HHMMPM"],
}

FIXED_WIDTH_SCHEMA = {
    "PFP": {
        "sales": [
            {"name": "StoreID", "width": 22, "start": 1},
            {"name": "StoreName", "width": 50, "start": 23},
            {"name": "DistRep", "width": 30, "start": 73},
            {"name": "UPC", "width": 20, "start": 103},
            {"name": "Description", "width": 50, "start": 123},
            {"name": "TranDt", "width": 10, "start": 173},
            {"name": "Qty", "width": 12, "start": 183},
            {"name": "UOM", "width": 2, "start": 195},
            {"name": "UOMCnt", "width": 12, "start": 197},
            {"name": "TotalSales", "width": 12, "start": 209},
            {"name": "Warehouse", "width": 28, "start": 221},
        ],
        "store_mapping": [
            {"name": "StoreID", "width": 22, "start": 1},
            {"name": "StoreName", "width": 50, "start": 23},
            {"name": "Address", "width": 50, "start": 73},
            {"name": "City", "width": 50, "start": 123},
            {"name": "State", "width": 2, "start": 173},
            {"name": "Zip", "width": 10, "start": 175},
            {"name": "Phone", "width": 15, "start": 185},
            {"name": "Dist Rep", "width": 30, "start": 200},
        ],
        "inventory": [
            {"name": "StoreID", "width": 20, "start": 1},
            {"name": "InvDT", "width": 10, "start": 21},
            {"name": "UPC", "width": 20, "start": 31},
            {"name": "Description", "width": 50, "start": 51},
            {"name": "QTY", "width": 12, "start": 101},
            {"name": "UOM", "width": 2, "start": 113},
            {"name": "UOMCnt", "width": 10, "start": 115},
        ],
    }
}

# Column mappings with aliases and expected data types
COLUMN_SCHEMA = {
    "sales": {
        "required": {
            "StoreID": {
                "type": ["numeric", "string"],
                "aliases": [
                    "store_id",
                    "customer_id",
                    "shipto_code",
                    "ship_to_id",
                    "Cust ID",
                ],
            },
            "TotalSales": {
                "type": ["numeric"],
                "aliases": [
                    "total_sales",
                    "sales_amount",
                    "Price",
                    "Total Sales",
                    "extended_price",
                ],
            },
            "TranDt": {
                "type": ["date"],
                "aliases": [
                    "transaction_date",
                    "sale_date",
                    "post_date",
                    "Inv Date",
                    "ship_date",
                ],
            },
            "UPC": {
                "type": ["numeric", "string"],
                "aliases": [
                    "product_code",
                    "item_code",
                    "alt_part1",
                    "UPC",
                    "upc_code",
                ],
            },
        },
        "optional": {
            "StoreName": {"type": "string"},
            "Address": {"type": "string"},
            "Quantity": {"type": "numeric"},
        },
    },
    "inventory": {
        "required": {
            "StoreID": {
                "type": ["numeric", "string"],
                "aliases": [
                    "location_id",
                    "warehouse_id",
                    "DC Name",
                    "DC_Name",
                    "DCName",
                ],
            },
            "InvDt": {
                "type": ["date"],
                "aliases": ["inventory_date", "stock_date"],
            },
            "Qty": {
                "type": ["numeric", "string"],
                "aliases": ["quantity", "stock_qty"],
            },
            "UPC": {"type": ["numeric", "string"], "aliases": ["product_code"]},
        }
    },
    "store_mapping": {
        "required": {
            "StoreID": {
                "type": ["numeric", "string"],
                "aliases": [
                    "location_id",
                    "ship_to_id",
                    "warehouse_id",
                    "DC Name",
                    "DC_Name",
                    "DCName",
                ],
            }
        }
    },
}

# Minimum columns required for each file type
MIN_COLUMNS = {"sales": 4, "inventory": 4, "store_mapping": 1}
