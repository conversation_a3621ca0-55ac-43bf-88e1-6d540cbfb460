import json
from pydantic import BaseModel, Field, validator
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from sqlalchemy import JSON


class CustomUploadFilesBase(BaseModel):
    fileName: str
    uploaderName: str
    distributorName: Optional[str] = Field(default=None)
    fileType: str
    fileSize: int
    blobUrl: str
    errorFileUrl: Optional[str]
    isValidated: Optional[bool] = Field(default=False)
    validationErrors: Optional[Union[List[Union[str, Dict[str, Any]]], str]] = Field(
        default_factory=list
    )

    @validator("validationErrors", pre=True)
    def parse_validation_errors(cls, v):
        if v is None:
            return []
        if isinstance(v, str):
            try:
                parsed = json.loads(v)
                return parsed if isinstance(parsed, list) else [parsed]
            except json.JSONDecodeError:
                return []
        return v if isinstance(v, list) else [v]


class CustomUploadFilesCreate(CustomUploadFilesBase):
    uploadDate: Optional[datetime] = Field(default_factory=datetime.utcnow)
    updatedDate: Optional[datetime] = Field(default=None)


class CustomUploadFilesUpdate(BaseModel):
    fileName: Optional[str] = Field(default=None)
    uploaderName: Optional[str] = Field(default=None)
    distributorName: Optional[str] = Field(default=None)
    fileType: Optional[str] = Field(default=None)
    fileSize: Optional[int] = Field(default=None)
    blobUrl: Optional[str] = Field(default=None)
    errorFileUrl: Optional[str] = Field(default=False)
    isValidated: Optional[bool] = Field(default=False)
    validationErrors: Optional[Union[List[str], str]] = Field(default_factory=list)
    updatedDate: Optional[datetime] = Field(default_factory=datetime.utcnow)


class CustomUploadFilesFilters(BaseModel):
    fileName: Optional[str] = Field(default=None)
    uploaderName: Optional[str] = Field(default=None)
    distributorName: Optional[str] = Field(default=None)
    fileType: Optional[str] = Field(default=None)
    minUploadDate: Optional[datetime] = Field(default=None)
    maxUploadDate: Optional[datetime] = Field(default=None)
    isValidated: Optional[bool] = Field(default=False)
    searchValue: Optional[str] = Field(default=None)


class CustomUploadFilesResponse(CustomUploadFilesBase):
    fileID: int
    uploadDate: datetime
    updatedDate: Optional[datetime] = Field(default=None)

    @validator("validationErrors", pre=True)
    def ensure_consistent_format(cls, v):
        if v is None:
            return []
        if isinstance(v, str):
            try:
                parsed = json.loads(v)
                return parsed if isinstance(parsed, list) else [parsed]
            except json.JSONDecodeError:
                return []
        return v if isinstance(v, list) else [v]

    class Config:
        from_attributes = True


class ValidationCounts(BaseModel):
    validated: int
    not_validated: int
    total: int


class PaginatedCustomUploadFilesResponse(BaseModel):
    files: List[CustomUploadFilesResponse]
    totalCount: int
    validationCounts: ValidationCounts
