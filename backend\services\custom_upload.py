import logging
import uuid
from io import By<PERSON><PERSON>
from typing import List, Optional, BinaryIO
from concurrent.futures import Thr<PERSON>PoolExecutor

from azure.storage.blob import (
    BlobServiceClient,
    BlobClient,
    ContainerClient,
    BlobBlock,
    ContentSettings,
)
from azure.storage.filedatalake import DataLakeServiceClient
from azure.identity import (
    ClientSecretCredential,
    ManagedIdentityCredential,
    DefaultAzureCredential,
)
from fastapi import UploadFile, HTTPException
from azure.core.exceptions import ResourceExistsError, ResourceNotFoundError
from config.azure_config import settings

from azure.keyvault.secrets import SecretClient


class AzureBlobService:
    """Service for interacting with Azure Blob Storage using DataLake approach"""

    def __init__(self):
        """Initialize the Azure Blob Service client using DataLake pattern"""
        try:
            # """ Get Service Principal Secret """

            # kv_uri = "https://npddceus2devakv.vault.azure.net"

            # credential = DefaultAzureCredential()
            # client = SecretClient(vault_url=kv_uri, credential=credential)

            # retrieved_client = client.get_secret("SpClientId")

            # retrieved_secret = client.get_secret("SpSecret")

            print("1----", settings.AZURE_STORAGE_CONTAINER_NAME)
            print(
                "2-----",
                settings.AZURE_CLIENT_ID,
                # retrieved_client.value,
                # retrieved_secret.value,
            )
            print("3----", settings.AZURE_STORAGE_CONTAINER_NAME)
            print("4----", settings.AZURE_USE_MSI)
            self.container_name = settings.AZURE_STORAGE_CONTAINER_NAME
            if not self.container_name:
                raise ValueError("Azure storage container name is not configured")

            # Use MSI when in Azure, client secret when local
            if settings.AZURE_USE_MSI:
                print("inside managed")
                credential = DefaultAzureCredential(
                    # client_id=retrieved_client.value,
                    # client_secret=retrieved_secret.value,
                )
            else:
                logging.info("inside client creds")
                credential = ClientSecretCredential(
                    tenant_id=settings.AZURE_TENANT_ID,
                    client_id=settings.AZURE_CLIENT_ID,
                    client_secret=settings.AZURE_CLIENT_SECRET,
                )

            # Initialize DataLakeServiceClient instead of BlobServiceClient
            self.datalake_service_client = DataLakeServiceClient(
                account_url=settings.AZURE_STORAGE_ACCOUNT_URL, credential=credential
            )

            # Get file system client (equivalent to container client)
            self.file_system_client = (
                self.datalake_service_client.get_file_system_client(self.container_name)
            )

            # Create container if it doesn't exist
            # self._ensure_container_exists()

        except Exception as e:
            logging.error(f"Failed to initialize AzureBlobService: {str(e)}")
            raise

    def _ensure_container_exists(self):
        """Create the container if it doesn't exist"""
        try:
            # Try to get properties to check if container exists
            self.file_system_client.get_file_system_properties()
        except ResourceNotFoundError:
            # Create the container if it doesn't exist
            self.file_system_client.create_file_system()

    async def upload_file_from_bytes(
        self,
        content: bytes,
        blob_name: str,
        folder_path: str = "",
        content_type: str = "text/csv",
    ) -> str:
        """
        Uploads file content directly from bytes to Azure Blob Storage.

        Args:
            content: The file content as bytes.
            blob_name: The desired name of the blob in storage (including folder path if any).
            content_type: The content type of the blob (e.g., "text/plain", "text/csv").

        Returns:
            The URL of the uploaded blob.
        """
        try:
            blob_path = f"{folder_path}/{blob_name}" if folder_path else blob_name
            directory_name, file_name = self._get_dir_file_name(blob_path)

            directory_client = self.file_system_client.get_directory_client(
                directory_name
            )
            file_client = directory_client.create_file(file_name)

            file_client.upload_data(
                content,
                overwrite=True,
                content_settings=ContentSettings(content_type=content_type),
            )

            return f"{self.file_system_client.url}/{blob_path}"
        except Exception as e:
            logging.error(f"Failed to upload blob from bytes {blob_name}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload file to blob storage: {str(e)}",
            )

    async def upload_file(self, file: UploadFile, folder_path: str = "") -> str:
        """
        Upload a file to Azure Blob Storage

        Args:
            file: The file to upload
            folder_path: Optional folder path within the container

        Returns:
            The blob URL
        """
        try:
            blob_path = (
                f"{folder_path}/{file.filename}" if folder_path else file.filename
            )
            file_content = await file.read()

            directory_name, file_name = self._get_dir_file_name(blob_path)
            directory_client = self.file_system_client.get_directory_client(
                directory_name
            )
            file_client = directory_client.create_file(file_name)

            file_client.upload_data(file_content, overwrite=True)
            return f"{self.file_system_client.url}/{blob_path}"
        except Exception as e:
            logging.error(f"Failed to upload file {file.filename}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload file: {str(e)}",
            )

    async def upload_large_file(self, file: UploadFile, folder_path: str = "") -> str:
        """
        Upload a large file to Azure Blob Storage using chunks

        Args:
            file: The file to upload
            folder_path: Optional folder path within the container

        Returns:
            The blob URL
        """
        try:
            blob_path = (
                f"{folder_path}/{file.filename}" if folder_path else file.filename
            )
            file_content = await file.read()

            directory_name, file_name = self._get_dir_file_name(blob_path)
            directory_client = self.file_system_client.get_directory_client(
                directory_name
            )
            file_client = directory_client.create_file(file_name)

            chunk_size = 4 * 1024 * 1024  # 4MB chunks
            offset = 0
            file_stream = BytesIO(file_content)

            while True:
                read_data = file_stream.read(chunk_size)
                if not read_data:
                    break

                file_client.append_data(
                    data=read_data, offset=offset, length=len(read_data)
                )
                offset += len(read_data)

            file_client.flush_data(offset)
            return f"{self.file_system_client.url}/{blob_path}"
        except Exception as e:
            logging.error(f"Failed to upload large file {file.filename}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload large file: {str(e)}",
            )

    def download_file(self, blob_name: str) -> bytes:
        """
        Download a file from Azure Blob Storage with better error handling

        Args:
            blob_name: The name of the blob to download

        Returns:
            The file content as bytes
        """
        try:
            directory_name, file_name = self._get_dir_file_name(blob_name)
            directory_client = self.file_system_client.get_directory_client(
                directory_name
            )
            file_client = directory_client.get_file_client(file_name)

            download_stream = file_client.download_file()
            return download_stream.readall()

        except ResourceNotFoundError:
            available_files = [f.name for f in self.file_system_client.get_paths()]
            raise HTTPException(
                status_code=404,
                detail={
                    "message": f"File {blob_name} not found",
                    "available_files": available_files,
                    "container": self.container_name,
                },
            )
        except Exception as e:
            logging.error(f"Download failed for {blob_name}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

    def list_files(self, folder_path: str = "") -> List[dict]:
        """
        List all files in a folder

        Args:
            folder_path: Optional folder path within the container

        Returns:
            List of file information dictionaries
        """
        try:
            paths = self.file_system_client.get_paths(path=folder_path)

            files = []
            for path in paths:
                if not path.is_directory:  # Only include files, not directories
                    file_client = self.file_system_client.get_file_client(path.name)
                    properties = file_client.get_file_properties()

                    files.append(
                        {
                            "name": path.name,
                            "size": properties.size,
                            "content_type": properties.content_settings.content_type,
                            "last_modified": properties.last_modified,
                            "url": f"{self.file_system_client.url}/{path.name}",
                        }
                    )

            return files
        except Exception as e:
            logging.error(f"Failed to list files in {folder_path}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to list files: {str(e)}",
            )

    def delete_file(self, blob_name: str) -> bool:
        """
        Delete a file from Azure Blob Storage

        Args:
            blob_name: The name of the blob to delete

        Returns:
            True if deleted successfully
        """
        try:
            directory_name, file_name = self._get_dir_file_name(blob_name)
            file_client = self.file_system_client.get_file_client(
                f"{directory_name}/{file_name}"
            )
            file_client.delete_file()
            return True
        except ResourceNotFoundError:
            raise HTTPException(status_code=404, detail=f"File {blob_name} not found")
        except Exception as e:
            logging.error(f"Failed to delete file {blob_name}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete file: {str(e)}",
            )

    def _get_dir_file_name(self, blob_path: str) -> tuple:
        """
        Helper method to split blob path into directory and file name

        Args:
            blob_path: Full path to the blob

        Returns:
            tuple: (directory_name, file_name)
        """
        if "/" in blob_path:
            parts = blob_path.rsplit("/", 1)
            return (parts[0], parts[1])
        return ("", blob_path)
