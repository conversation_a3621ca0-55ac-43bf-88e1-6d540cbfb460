from datetime import datetime
import json
from typing import Dict, List, Optional, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_, case, desc, or_
from fastapi import HTTPException

from models.custom_upload_model import CustomUploadFiles
from schema.custom_upload_schema import (
    CustomUploadFilesCreate,
    CustomUploadFilesUpdate,
    CustomUploadFilesFilters,
)


class CustomUploadFilesService:
    """Service to handle CustomUploadFiles operations"""

    def get(self, db: Session, file_id: int) -> Optional[CustomUploadFiles]:
        return (
            db.query(CustomUploadFiles)
            .filter(CustomUploadFiles.fileID == file_id)
            .first()
        )

    def get_list(
        self,
        db: Session,
        uploader_name: Optional[str] = None,
        file_type: Optional[str] = None,
        search_query: Optional[str] = None,
        limit: int = 20,
        page: int = 1,
    ) -> Tuple[List[Dict], int, Dict[str, int]]:  # Added validation counts in return
        query = db.query(CustomUploadFiles)

        filter_conditions = []

        # Add uploader name filter if provided
        if uploader_name:
            filter_conditions.append(
                CustomUploadFiles.uploaderName.ilike(f"%{uploader_name}%")
            )

        # Add file type filter if provided
        if file_type:
            filter_conditions.append(CustomUploadFiles.fileType.ilike(f"%{file_type}%"))

        # Add search query filter if provided
        if search_query:
            filter_conditions.append(
                or_(
                    CustomUploadFiles.fileName.ilike(f"%{search_query}%"),
                    CustomUploadFiles.uploaderName.ilike(f"%{search_query}%"),
                    CustomUploadFiles.distributorName.ilike(f"%{search_query}%"),
                )
            )

        # # Add other filters if provided
        # if filters:
        #     if filters.fileName:
        #         filter_conditions.append(
        #             CustomUploadFiles.fileName.ilike(f"%{filters.fileName}%")
        #         )
        #     if filters.distributorName:
        #         filter_conditions.append(
        #             CustomUploadFiles.distributorName.ilike(
        #                 f"%{filters.distributorName}%"
        #             )
        #         )
        #     if filters.fileType:
        #         filter_conditions.append(CustomUploadFiles.fileType == filters.fileType)
        #     if filters.minUploadDate:
        #         filter_conditions.append(
        #             CustomUploadFiles.uploadDate >= filters.minUploadDate
        #         )
        #     if filters.maxUploadDate:
        #         filter_conditions.append(
        #             CustomUploadFiles.uploadDate <= filters.maxUploadDate
        #         )

        if filter_conditions:
            query = query.filter(and_(*filter_conditions))

        # Get total count before applying limit and offset
        total_count = query.count()

        validation_counts = {
            "validated": query.filter(CustomUploadFiles.isValidated == True).count(),
            "not_validated": query.filter(
                CustomUploadFiles.isValidated == False
            ).count(),
            "total": total_count,
        }
        # Order by upload date (newest first) and then by updated date (newest first)
        query = query.order_by(
            case(
                (
                    CustomUploadFiles.updatedDate == None,
                    0,
                ),  # Files without updates get 0
                else_=1,  # Files with updates get 1
            ),
            desc(CustomUploadFiles.updatedDate),  # Most recently updated first
            desc(CustomUploadFiles.uploadDate),  # Most recently uploaded first
        )

        paginated_files = query.offset((page - 1) * limit).limit(limit).all()

        return paginated_files, total_count, validation_counts  # Return both

    def create_or_update(
        self, db: Session, obj_in: CustomUploadFilesCreate
    ) -> CustomUploadFiles:
        data = obj_in.dict()

        # Handle validationErrors conversion
        if "validationErrors" in data:
            if data["validationErrors"] is None:
                data["validationErrors"] = None
            elif isinstance(data["validationErrors"], (list, dict)):
                data["validationErrors"] = (
                    json.dumps(data["validationErrors"])
                    if data["validationErrors"]
                    else None
                )

        existing = (
            db.query(CustomUploadFiles)
            .filter(CustomUploadFiles.fileName == obj_in.fileName)
            .first()
        )

        if existing:
            return self.update(db, existing.fileID, CustomUploadFilesUpdate(**data))

        db_obj = CustomUploadFiles(**data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, file_id: int, obj_in: CustomUploadFilesUpdate
    ) -> CustomUploadFiles:
        db_obj = (
            db.query(CustomUploadFiles)
            .filter(CustomUploadFiles.fileID == file_id)
            .first()
        )
        if not db_obj:
            raise HTTPException(
                status_code=404, detail=f"File with ID {file_id} not found"
            )

        update_data = obj_in.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_obj, key, value)

        db_obj.updatedDate = datetime.utcnow()
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, file_id: int) -> None:
        db_obj = (
            db.query(CustomUploadFiles)
            .filter(CustomUploadFiles.fileID == file_id)
            .first()
        )
        if not db_obj:
            raise HTTPException(
                status_code=404, detail=f"File with ID {file_id} not found"
            )

        db.delete(db_obj)
        db.commit()
